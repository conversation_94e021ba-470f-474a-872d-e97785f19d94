package machineid

import "testing"

func TestID(t *testing.T) {
	got, err := ID()
	if err != nil {
		t.<PERSON><PERSON><PERSON>(err)
	}
	if got == "" {
		t.<PERSON>rror("Got empty machine id")
	}
}

func TestProtectedID(t *testing.T) {
	id, err := ID()
	if err != nil {
		t.<PERSON>rror(err)
	}
	hash, err := ProtectedID("app.id")
	if err != nil {
		t.<PERSON><PERSON>r(err)
	}
	if hash == "" {
		t.Error("Got empty machine id hash")
	}
	if id == hash {
		t.<PERSON>rror("id and hashed id are the same")
	}
}
